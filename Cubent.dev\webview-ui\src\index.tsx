import { StrictMode } from "react"
import { createRoot } from "react-dom/client"

import "./index.css"
import App from "./App"
import "../node_modules/@vscode/codicons/dist/codicon.css"

import { getHighlighter } from "./utils/highlighter"

// Add global error handler to catch and log more details about errors
window.addEventListener('error', (event) => {
	console.error('Global error caught:', {
		message: event.message,
		filename: event.filename,
		lineno: event.lineno,
		colno: event.colno,
		error: event.error,
		stack: event.error?.stack
	})
})

window.addEventListener('unhandledrejection', (event) => {
	console.error('Unhandled promise rejection:', {
		reason: event.reason,
		promise: event.promise
	})
})

// Initialize <PERSON><PERSON> early to hide initialization latency (async)
getHighlighter().catch((error: Error) => console.error("Failed to initialize Shiki highlighter:", error))

try {
	const rootElement = document.getElementById("root")
	if (!rootElement) {
		throw new Error("Root element not found")
	}

	createRoot(rootElement).render(
		<StrictMode>
			<App />
		</StrictMode>,
	)
} catch (error) {
	console.error("Failed to render React app:", error)
}
